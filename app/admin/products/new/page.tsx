"use client";

import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { useToast } from "@/hooks/use-toast";
import React, { useState, useEffect } from "react";
import { Card, CardHeader, CardTitle, CardContent } from "@/components/ui/card";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
  FormDescription,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { Textarea } from "@/components/ui/textarea";
import ReactMarkdown from "react-markdown";
import { Ta<PERSON>, Ta<PERSON>Content, TabsList, TabsTrigger } from "@/components/ui/tabs";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import { z } from "zod";
import {
  Package2,
  DollarSign,
  Truck,
  CheckIcon,
  ChevronsUpDownIcon,
} from "lucide-react";
// uuid import removed — use the client-friendly generateUUID helper defined later in this file
import { ProductService } from "@/services/product.service";
import {
  Category,
  CategoryService,
  CategoryVariantAttribute,
} from "@/services/category.service";
import { ProductCreationData } from "@/data/models/product.model";
import { cn } from "@/lib/utils";
import { Checkbox } from "@/components/ui/checkbox";
import { categories } from "@/lib/constants";
import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
  CommandList,
} from "@/components/ui/command";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";

// Client-friendly UUID generator (works in modern browsers / serverless runtimes).
// Use `generateUUID()` instead of importing `uuid` to avoid requiring the `uuid` package
// at runtime in environments where it's not available.
const generateUUID = (): string => {
  // Prefer the native crypto.randomUUID when available
  if (
    typeof crypto !== "undefined" &&
    typeof (crypto as any).randomUUID === "function"
  ) {
    return (crypto as any).randomUUID();
  }

  // Fallback: lightweight RFC4122 v4 style generator (not cryptographically strong)
  return "xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx".replace(/[xy]/g, (c) => {
    const r = (Math.random() * 16) | 0;
    const v = c === "x" ? r : (r & 0x3) | 0x8;
    return v.toString(16);
  });
};

const shippingRateSchema = z.object({
  to_country: z.enum(["UK", "Ghana", "Nigeria", "Rest Of Africa"]),
  to_zone: z.union([
    z.enum(["UK", "Accra", "Outside Accra", "Lagos", "Outside Lagos"]),
    z.literal(""),
  ]),
  base_rate: z.number().min(0),
  duty: z.number().min(0),
  rate_per_kg: z.number().min(0).optional(),
  estimated_delivery_days: z.number().min(0).optional(),
  currency: z.string(),
});

const shippingAndDutySchema = z.object({
  to_country: z.enum(["Ghana", "Nigeria"]),
  duty: z.number(),
  currency: z.string().optional(),
});

const externalDetailsSchema = z.object({
  origin_name: z.string().min(1),
  origin_url: z.string().url(),
  is_affiliate: z.boolean(),
  discount_code: z.string().optional(),
});

const internalDetailsSchema = z.object({
  location: z.enum(["UK", "Ghana"]),
  weight_kg: z.number(),
  dimensions_cm: z.object({
    length: z.number(),
    width: z.number(),
    height: z.number(),
  }),
});

// Individual variant item schema - matches the structure in ProductVariant.variants array
const variantItemSchema = z
  .object({
    variant_id: z.string().optional(),
    option_values: z.record(z.string()), // The selected values for each attribute
    sale_price: z.number().optional(),
    price: z.number().optional(),
    stock: z.number().optional(),
    discount_percent: z.number().optional(),
  })
  .passthrough(); // Allow additional fields

// Master option schema - matches ProductVariant.master_options structure
const masterOptionSchema = z.record(z.array(z.string()));

// Complete variants schema that matches ProductVariant interface
const productVariantsSchema = z
  .object({
    variants: z.array(variantItemSchema),
    master_options: z.array(masterOptionSchema),
  })
  .optional();

const formSchema = z.object({
  title: z.string().min(1, { message: "Title is required" }),
  description: z.string().min(1, { message: "Description is required" }),
  currency: z.string().min(1),
  type: z.enum(["external", "internal"]),
  category: z.string().min(1),
  subcategory: z.string().min(1),
  brand: z.string().optional(),
  tags: z.array(z.string()).optional(),
  condition: z.enum(["New", "Used"]),
  admin_notes: z.string().optional(),
  origin_location: z.enum(["UK", "Ghana"]),
  // Compact shipping and duty entries - form will validate this and we will rebuild detailed shipping_rates on submit
  shipping_and_duty: z.array(shippingAndDutySchema),
  details: z.union([externalDetailsSchema, internalDetailsSchema]),
  refundable: z.boolean(),
  refund_policy: z.string().min(1, { message: "Refund policy is required" }),
  variants: productVariantsSchema.refine(
    (variants) => {
      return variants && variants.variants && variants.variants.length > 0;
    },
    { message: "At least one variant is required" },
  ),
});

const productService = new ProductService();
const categoryService = new CategoryService();

export default function NewProductPage() {
  const { toast } = useToast();
  const [isLoading, setIsLoading] = useState(false);
  const [categories, setCategories] = useState<Category[]>([]);
  const [primaryImage, setPrimaryImage] = useState<File | null>(null);
  const [primaryImagePreview, setPrimaryImagePreview] = useState<string>("");
  const primaryImageInputRef = React.useRef<HTMLInputElement | null>(null);
  const [sharedImages, setSharedImages] = useState<File[]>([]);
  const [sharedImagePreviews, setSharedImagePreviews] = useState<string[]>([]);
  const sharedImagesInputRef = React.useRef<HTMLInputElement | null>(null);

  // Variant-related state
  const [variantAttributes, setVariantAttributes] = useState<
    CategoryVariantAttribute[]
  >([]);
  const [variantImages, setVariantImages] = useState<{
    [variantIndex: number]: File[];
  }>({});
  const [variantImagePreviews, setVariantImagePreviews] = useState<{
    [variantIndex: number]: string[];
  }>({});

  // State for custom variant attribute values
  const [customAttributeValues, setCustomAttributeValues] = useState<
    Array<{
      variantIndex: number;
      attribute: string;
      value: string;
    }>
  >([]);

  // Helper functions for custom attribute values
  const getCustomAttributeValue = (
    variantIndex: number,
    attribute: string,
  ): string => {
    const customValue = customAttributeValues.find(
      (item) =>
        item.variantIndex === variantIndex && item.attribute === attribute,
    );
    return customValue?.value || "";
  };

  const setCustomAttributeValue = (
    variantIndex: number,
    attribute: string,
    value: string,
  ) => {
    setCustomAttributeValues((prev) => {
      const filtered = prev.filter(
        (item) =>
          !(item.variantIndex === variantIndex && item.attribute === attribute),
      );
      if (value.trim()) {
        return [...filtered, { variantIndex, attribute, value }];
      }
      return filtered;
    });
  };

  // Brand-related state
  const [availableBrands, setAvailableBrands] = useState<string[]>([]);

  // Fetch categories on component mount
  useEffect(() => {
    const fetchCategories = async () => {
      try {
        const categoriesData = await categoryService.getCategories();
        setCategories(categoriesData);
      } catch (error) {
        console.error("Error fetching categories:", error);
        toast({
          title: "Error",
          description: "Failed to load categories",
          variant: "destructive",
        });
      }
    };

    fetchCategories();
  }, []);

  const refundPolicyMsg = {
    true: "Item is refundable. Contact our support.",
    false: "Item is non refundable. Contact our support for any issues.",
  };

  const getDefaultValues = () => ({
    title: "",
    description: "",
    currency: "GBP",
    type: "internal" as const,
    category: "",
    subcategory: "",
    brand: "none",
    tags: [],
    condition: "New" as const,
    admin_notes: "",
    origin_location: "UK" as const,
    // compact shipping and duty defaults (duty left undefined so the input can be cleared; validation still requires a number)
    shipping_and_duty: [
      {
        to_country: "Ghana" as const,
        duty: undefined,
        currency: "GBP",
      },
      {
        to_country: "Nigeria" as const,
        duty: undefined,
        currency: "GBP",
      },
    ],
    // keep the detailed rates array intact (will be rebuilt on submit)
    shipping_rates: [
      {
        to_country: "Ghana" as const,
        to_zone: "Accra" as const,
        base_rate: 0,
        duty: 0,
        currency: "GBP",
      },
      {
        to_country: "Ghana" as const,
        to_zone: "Outside Accra" as const,
        base_rate: 0,
        duty: 0,
        currency: "GBP",
      },
      {
        to_country: "Nigeria" as const,
        to_zone: "Lagos" as const,
        base_rate: 0,
        duty: 0,
        currency: "GBP",
      },
      {
        to_country: "Nigeria" as const,
        to_zone: "Outside Lagos" as const,
        base_rate: 0,
        duty: 0,
        currency: "GBP",
      },
      {
        to_country: "Rest Of Africa" as const,
        to_zone: "" as const,
        base_rate: 0,
        duty: 0,
        currency: "GBP",
      },
    ],

    // Required details for the default 'internal' type
    details: {
      weight_kg: undefined,
      dimensions_cm: undefined,
    },
    refundable: false,
    refund_policy: "",
    variants: undefined,
  });

  // Helper function to reset all file-related states and clear file inputs
  const resetFileStates = () => {
    // Clean up primary image
    if (primaryImagePreview) {
      URL.revokeObjectURL(primaryImagePreview);
    }
    setPrimaryImage(null);
    setPrimaryImagePreview("");

    // Clean up shared images
    sharedImagePreviews.forEach((url) => URL.revokeObjectURL(url));
    setSharedImages([]);
    setSharedImagePreviews([]);

    // Clean up variant images
    Object.values(variantImagePreviews)
      .flat()
      .forEach((url) => URL.revokeObjectURL(url));
    setVariantImages({});
    setVariantImagePreviews({});

    // Clear custom attribute values
    setCustomAttributeValues([]);

    // Clear file input elements by resetting their values
    const fileInputs = document.querySelectorAll('input[type="file"]');
    fileInputs.forEach((input: any) => {
      input.value = "";
    });
  };

  const form = useForm<z.infer<typeof formSchema>>({
    resolver: zodResolver(formSchema),
    defaultValues: getDefaultValues(),
    mode: "onTouched",
  });

  // Auto-calculate discount_percent for variants when price or sale_price changes
  useEffect(() => {
    const subscription = form.watch((_, { name }) => {
      if (
        name &&
        name.includes("variants.variants.") &&
        (name.includes(".price") || name.includes(".sale_price"))
      ) {
        const variantMatch = name.match(/variants\.variants\.(\d+)\./);
        if (variantMatch) {
          const variantIndex = parseInt(variantMatch[1]);
          const variants = form.getValues("variants");

          if (variants?.variants?.[variantIndex]) {
            const variant = variants.variants[variantIndex];
            const price = variant.price;
            const salePrice = variant.sale_price;

            if (price && salePrice && price > salePrice) {
              const discountPercent = Math.round(
                ((price - salePrice) / price) * 100,
              );
              form.setValue(
                `variants.variants.${variantIndex}.discount_percent` as any,
                discountPercent,
              );
            } else {
              form.setValue(
                `variants.variants.${variantIndex}.discount_percent` as any,
                undefined,
              );
            }
          }
        }
      }
    });

    return () => subscription.unsubscribe();
  }, [form]);

  const handlePrimaryImageChange = (
    event: React.ChangeEvent<HTMLInputElement>,
  ) => {
    const file = event.target.files?.[0];
    if (file) {
      // Clean up previous primary image preview
      if (primaryImagePreview) {
        URL.revokeObjectURL(primaryImagePreview);
      }

      setPrimaryImage(file);
      setPrimaryImagePreview(URL.createObjectURL(file));

      // Do NOT clear the underlying file input here so the browser continues
      // to show the chosen filename until the user explicitly removes it.
    }
  };

  const removePrimaryImage = () => {
    if (primaryImagePreview) {
      URL.revokeObjectURL(primaryImagePreview);
    }
    setPrimaryImage(null);
    setPrimaryImagePreview("");
    if (primaryImageInputRef.current) {
      primaryImageInputRef.current.value = "";
    }
  };

  // Shared image handling functions
  const handleSharedImagesChange = (
    event: React.ChangeEvent<HTMLInputElement>,
  ) => {
    const files = Array.from(event.target.files || []);
    setSharedImages((prev) => [...prev, ...files]);

    // Create preview URLs for the images
    const newPreviewUrls = files.map((file) => URL.createObjectURL(file));
    setSharedImagePreviews((prev) => [...prev, ...newPreviewUrls]);

    // Do NOT clear the underlying file input here so the browser continues
    // to show the chosen filenames until the user explicitly removes them.
  };

  const removeSharedImage = (imageIndex: number) => {
    // Revoke the URL to prevent memory leaks
    if (sharedImagePreviews[imageIndex]) {
      URL.revokeObjectURL(sharedImagePreviews[imageIndex]);
    }
    const newShared = sharedImages.filter((_, i) => i !== imageIndex);
    setSharedImages(newShared);
    setSharedImagePreviews((prev) => prev.filter((_, i) => i !== imageIndex));
    // If no more shared images, clear file input to remove filename
    if (sharedImagesInputRef.current && newShared.length === 0) {
      sharedImagesInputRef.current.value = "";
    }
  };

  // Variant image handling functions
  const handleVariantImagesChange = (
    variantIndex: number,
    event: React.ChangeEvent<HTMLInputElement>,
  ) => {
    const files = Array.from(event.target.files || []);
    setVariantImages((prev) => ({
      ...prev,
      [variantIndex]: [...(prev[variantIndex] || []), ...files],
    }));

    // Create preview URLs for the images
    const newPreviewUrls = files.map((file) => URL.createObjectURL(file));
    setVariantImagePreviews((prev) => ({
      ...prev,
      [variantIndex]: [...(prev[variantIndex] || []), ...newPreviewUrls],
    }));
  };

  const removeVariantImage = (variantIndex: number, imageIndex: number) => {
    setVariantImages((prev) => ({
      ...prev,
      [variantIndex]: (prev[variantIndex] || []).filter(
        (_, i) => i !== imageIndex,
      ),
    }));

    setVariantImagePreviews((prev) => {
      const currentPreviews = prev[variantIndex] || [];
      // Revoke the URL to prevent memory leaks
      if (currentPreviews[imageIndex]) {
        URL.revokeObjectURL(currentPreviews[imageIndex]);
      }
      return {
        ...prev,
        [variantIndex]: currentPreviews.filter((_, i) => i !== imageIndex),
      };
    });
  };

  const addVariant = () => {
    const currentVariants = form.getValues("variants");
    // Create option_values object for this variant
    const option_values: Record<string, any> = {};
    variantAttributes.forEach((attr) => {
      const hasCustom = (attr.values || []).includes("Custom");
      if (hasCustom) {
        // For attributes with custom option, default to first non-custom value as string
        const defaultValue =
          (attr.values || []).find((val) => val !== "Custom") || "";
        option_values[attr.name] = String(defaultValue);
      } else {
        // For regular attributes, use first value as string
        const defaultValue =
          attr.values && attr.values.length > 0 ? attr.values[0] : "";
        option_values[attr.name] = String(defaultValue);
      }
    });

    const newVariantItem = {
      option_values,
      sale_price: 0,
      price: 0,
      stock: 0,
      currency: "GBP",
    };

    if (currentVariants) {
      // Update existing variants structure
      const updatedVariants = {
        ...currentVariants,
        variants: [...(currentVariants.variants || []), newVariantItem],
      };
      form.setValue("variants", updatedVariants);
    } else {
      // Create new variants structure
      const newVariants = {
        variants: [newVariantItem],
        master_options: variantAttributes.map((attr) => ({
          [attr.name]: (attr.values || [])
            .filter((val) => val !== "Custom")
            .map(String),
        })),
      };
      form.setValue("variants", newVariants);
    }
  };

  const removeVariant = (index: number) => {
    const currentVariants = form.getValues("variants");
    if (currentVariants && currentVariants.variants) {
      const updatedVariants = {
        ...currentVariants,
        variants: currentVariants.variants.filter(
          (_: any, i: number) => i !== index,
        ),
      };
      form.setValue("variants", updatedVariants);
    }

    // Clean up variant images and previews
    const currentVariantPreviews = variantImagePreviews[index] || [];
    currentVariantPreviews.forEach((url) => URL.revokeObjectURL(url));

    setVariantImages((prev) => {
      const newState = { ...prev };
      delete newState[index];
      return newState;
    });

    setVariantImagePreviews((prev) => {
      const newState = { ...prev };
      delete newState[index];
      return newState;
    });

    // Clean up custom attribute values for this variant
    setCustomAttributeValues((prev) =>
      prev.filter((item) => item.variantIndex !== index),
    );
  };

  const onSubmit = async (data: z.infer<typeof formSchema>) => {
    if (!primaryImage) {
      toast({
        title: "Error",
        description: "Please select a primary product image",
        variant: "destructive",
      });
      return;
    }

    try {
      setIsLoading(true);

      // Prepare variants with proper master_options, UUIDs, and calculated discount_percent
      let preparedVariants = data.variants;

      if (preparedVariants && preparedVariants.variants) {
        // 1. Generate UUIDs for each variant and calculate discount_percent
        const variantsWithIds = preparedVariants.variants.map(
          (variant, variantIndex) => {
            // Transform custom values from string to object format
            const transformedOptionValues: Record<string, any> = {};

            Object.entries(variant.option_values).forEach(([key, value]) => {
              if (typeof value === "string") {
                if (value === "Custom") {
                  // Check if we have a custom value for this attribute in our custom state
                  const customValue = getCustomAttributeValue(
                    variantIndex,
                    key,
                  );
                  if (customValue.trim()) {
                    transformedOptionValues[key] = {
                      value: customValue,
                      is_custom: true,
                    };
                  } else {
                    // No custom value provided, keep as "Custom" (this shouldn't happen in normal flow)
                    transformedOptionValues[key] = value;
                  }
                } else {
                  // Regular predefined value
                  transformedOptionValues[key] = value;
                }
              } else {
                // Already in object format (shouldn't happen with new logic, but handle for safety)
                transformedOptionValues[key] = value;
              }
            });

            return {
              ...variant,
              option_values: transformedOptionValues,
              variant_id: variant.variant_id || generateUUID(),
              discount_percent:
                variant.sale_price && variant.price
                  ? Math.round(
                      ((variant.price - variant.sale_price) / variant.price) *
                        100,
                    )
                  : undefined,
            };
          },
        );

        // 2. Create master_options based on actual option_values in variants
        const master_options: Array<Record<string, Array<string>>> = [];

        if (variantAttributes.length > 0) {
          variantAttributes.forEach((attr) => {
            // Get all unique values for this attribute from actual variants
            const valuesInVariants = new Set<string>();
            variantsWithIds.forEach((variant) => {
              if (variant.option_values[attr.name]) {
                const optionValue = variant.option_values[attr.name] as any;

                if (typeof optionValue === "object" && optionValue?.value) {
                  if (optionValue.is_custom) {
                    // Custom value - add the actual custom value to master_options
                    valuesInVariants.add(optionValue.value);
                  } else {
                    // Object format for non-custom values (shouldn't happen with new logic, but handle for safety)
                    valuesInVariants.add(optionValue.value);
                  }
                } else if (
                  typeof optionValue === "string" &&
                  optionValue !== "Custom"
                ) {
                  // String format for regular predefined values
                  valuesInVariants.add(optionValue);
                }
                // Skip "Custom" string as it's just a placeholder
              }
            });

            // Convert Set to Array and only include if there are actual values used
            const actualValues = Array.from(valuesInVariants);

            // Only add to master_options if there are actual values used in variants
            if (actualValues.length > 0) {
              master_options.push({
                [attr.name]: actualValues,
              });
            }
          });
        }

        preparedVariants = {
          variants: variantsWithIds,
          master_options,
        };
      }

      // Extract product data and variants
      // Exclude `shipping_and_duty` from the payload we send to the API.
      // `shipping_and_duty` is only used client-side to rebuild `shipping_rates`
      // and must not be stored directly in the database.
      const { currency, variants, shipping_and_duty, ...productData } = data;

      // Transform brand value: convert "none" to undefined for database storage
      // Rebuild `shipping_rates` from compact `shipping_and_duty` entries
      const shippingAndDuty = (data as any).shipping_and_duty || [];
      const rebuiltShippingRates: any[] = [];

      // Duty is required for compact shipping entries. If any entry is missing duty,
      // abort submit with a clear error so we don't silently default to 0.
      shippingAndDuty.forEach((entry: any, idx: number) => {
        if (!entry || !entry.to_country) return;
        const dutyValue = entry.duty;
        if (dutyValue === undefined || dutyValue === null) {
          // Throw to be caught by outer try/catch so user sees error toast
          throw new Error(
            `Duty is required for ${entry.to_country} (shipping entry index ${idx}).`,
          );
        }

        const currency = entry.currency || "GBP";
        if (entry.to_country === "Ghana") {
          rebuiltShippingRates.push({
            to_country: "Ghana",
            to_zone: "Accra",
            base_rate: 0,
            duty: dutyValue,
            currency,
          });
          rebuiltShippingRates.push({
            to_country: "Ghana",
            to_zone: "Outside Accra",
            base_rate: 0,
            duty: dutyValue,
            currency,
          });
        } else if (entry.to_country === "Nigeria") {
          rebuiltShippingRates.push({
            to_country: "Nigeria",
            to_zone: "Lagos",
            base_rate: 0,
            duty: dutyValue,
            currency,
          });
          rebuiltShippingRates.push({
            to_country: "Nigeria",
            to_zone: "Outside Lagos",
            base_rate: 0,
            duty: dutyValue,
            currency,
          });
        }
      });

      const transformedProductData = {
        ...productData,
        brand: productData.brand === "none" ? undefined : productData.brand,
        shipping_rates: rebuiltShippingRates,
      };

      // Extract primary data from the first variant
      const firstVariant = preparedVariants?.variants?.[0];
      if (!firstVariant) {
        throw new Error("At least one variant is required");
      }

      // Create ProductCreationData object
      const creationData: ProductCreationData = {
        product: {
          ...transformedProductData,
          tags: data.tags || [],

          primary_data: {
            sale_price: firstVariant.sale_price || 0,
            price: firstVariant.price || 0,
            discount_percent: firstVariant.discount_percent || 0,
          },
          currency: "GBP",
          variants: preparedVariants,
          primary_image: { url: "", name: "" }, // Will be set by the service
          shared_images: [], // Will be set by the service
        },
        primaryImage,
        shared_images: sharedImages,
        variant_images: variantImages,
      };

      await productService.createProduct(creationData);

      toast({
        title: "Success",
        description: "Product created successfully",
      });

      // Reset form and clear all file-related states
      form.reset(getDefaultValues());
      resetFileStates();
    } catch (error: any) {
      // Show a generic error message to the user; avoid logging sensitive details to the console.
      toast({
        title: "Error",
        description: "Failed to create product",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  const productType = form.watch("type");
  const refundable = form.watch("refundable");

  useEffect(() => {
    // When type changes, reset the details field with appropriate structure
    // Only update if the current details structure doesn't match the expected type
    const currentDetails = form.getValues("details");

    if (productType === "external") {
      // Check if current details has external structure
      if (!currentDetails || !("origin_name" in currentDetails)) {
        form.setValue("details", {
          origin_name: "",
          origin_url: "",
          is_affiliate: false,
          discount_code: "",
        });
      }
    } else {
      // Check if current details has internal structure
      if (!currentDetails || !("location" in currentDetails)) {
        form.setValue("details", {
          location: "UK",
          weight_kg: 0,
          dimensions_cm: {
            length: 0,
            width: 0,
            height: 0,
          },
        });
      }
    }
  }, [productType, form]);

  // Update refund_policy based on refundable status
  useEffect(() => {
    const message = refundable ? refundPolicyMsg.true : refundPolicyMsg.false;
    form.setValue("refund_policy", message);
  }, [refundable, form]);

  // Watch for subcategory changes to fetch variant attributes
  const selectedSubcategory = form.watch("subcategory");

  useEffect(() => {
    const fetchCategoryData = async () => {
      if (selectedSubcategory) {
        try {
          // Clear variant attributes and custom values before fetching
          setVariantAttributes([]);
          setCustomAttributeValues([]);

          // Fetch variant attributes
          const attributes =
            await categoryService.getCategoryVariantAttributes(
              selectedSubcategory,
            );
          setVariantAttributes(attributes || []);

          // Fetch brands
          const brands =
            await categoryService.getCategoryBrands(selectedSubcategory);
          setAvailableBrands(brands || []);

          // Reset variants and brand when subcategory changes
          form.setValue("variants", undefined);
          form.setValue("brand", "none");
          setVariantImages({});
          setVariantImagePreviews({});

          // Automatically create a default variant if attributes exist
          if (attributes && attributes.length > 0) {
            // Create option_values object for the default variant
            const option_values: Record<string, any> = {};
            attributes.forEach((attr) => {
              const hasCustom = (attr.values || []).includes("Custom");
              if (hasCustom) {
                // For attributes with custom option, default to first non-custom value as string
                const defaultValue =
                  (attr.values || []).find((val) => val !== "Custom") || "";
                option_values[attr.name] = String(defaultValue);
              } else {
                // For regular attributes, use first value as string
                const defaultValue =
                  attr.values && attr.values.length > 0 ? attr.values[0] : "";
                option_values[attr.name] = String(defaultValue);
              }
            });

            const defaultVariantItem = {
              option_values,
              sale_price: 0,
              price: 0,
              stock: 0,
              currency: "GBP",
            };

            // Create new variants structure with default variant
            const newVariants = {
              variants: [defaultVariantItem],
              master_options: attributes.map((attr) => ({
                [attr.name]: (attr.values || [])
                  .filter((val) => val !== "Custom")
                  .map(String),
              })),
            };
            form.setValue("variants", newVariants);
          }
        } catch (error) {
          console.error("Error fetching category data:", error);
          setVariantAttributes([]);
          setAvailableBrands([]);
          setCustomAttributeValues([]);
        }
      } else {
        setVariantAttributes([]);
        setAvailableBrands([]);
        setCustomAttributeValues([]);
        form.setValue("variants", undefined);
        form.setValue("brand", "none");
        setVariantImages({});
        setVariantImagePreviews({});
      }
    };

    fetchCategoryData();
  }, [selectedSubcategory, categoryService, form]);

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(onSubmit)}>
        <div className="max-w-7xl mx-auto p-6">
          <div className="mb-8">
            <h1 className="text-3xl font-bold tracking-tight">New Product</h1>
            <p className="text-muted-foreground mt-2">
              Create a new product listing
            </p>
          </div>

          <div className="grid lg:grid-cols-2 gap-6 items-start">
            {/* Basic Information */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Package2 className="h-5 w-5" />
                  Basic Information
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <FormField
                  control={form.control}
                  name="title"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Title</FormLabel>
                      <FormControl>
                        <Input placeholder="Product title" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="description"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Description (Markdown Supported)</FormLabel>
                      <FormControl>
                        <Tabs defaultValue="write" className="w-full">
                          <TabsList className="grid w-full grid-cols-2">
                            <TabsTrigger value="write">Write</TabsTrigger>
                            <TabsTrigger value="preview">Preview</TabsTrigger>
                          </TabsList>
                          <TabsContent value="write">
                            <Textarea
                              placeholder="Product description (supports markdown: **bold**, *italic*, `code`, etc.)"
                              className="min-h-[150px]"
                              {...field}
                            />
                          </TabsContent>
                          <TabsContent value="preview">
                            <div className="min-h-[150px] p-3 border rounded-md bg-muted/50">
                              {field.value ? (
                                <div className="prose prose-sm max-w-none dark:prose-invert">
                                  <ReactMarkdown>{field.value}</ReactMarkdown>
                                </div>
                              ) : (
                                <div className="text-muted-foreground text-sm">
                                  Nothing to preview
                                </div>
                              )}
                            </div>
                          </TabsContent>
                        </Tabs>
                      </FormControl>
                      <FormDescription>
                        You can use markdown formatting. Switch to Preview tab
                        to see how it will look.
                      </FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <div className="grid grid-cols-2 gap-4">
                  <FormField
                    control={form.control}
                    name="type"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Product Type</FormLabel>
                        <Select
                          onValueChange={field.onChange}
                          defaultValue={field.value}
                        >
                          <FormControl>
                            <SelectTrigger>
                              <SelectValue placeholder="Select type" />
                            </SelectTrigger>
                          </FormControl>
                          <SelectContent>
                            <SelectItem value="internal">Internal</SelectItem>
                            <SelectItem value="external">External</SelectItem>
                          </SelectContent>
                        </Select>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="condition"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Condition</FormLabel>
                        <Select
                          onValueChange={field.onChange}
                          defaultValue={field.value}
                        >
                          <FormControl>
                            <SelectTrigger>
                              <SelectValue placeholder="Select condition" />
                            </SelectTrigger>
                          </FormControl>
                          <SelectContent>
                            <SelectItem value="New">New</SelectItem>
                            <SelectItem value="Used">Used</SelectItem>
                          </SelectContent>
                        </Select>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>

                <div className="grid grid-cols-2 gap-4">
                  <FormField
                    control={form.control}
                    name="category"
                    render={({ field }) => {
                      const [open, setOpen] = React.useState(false);
                      const parentCategories = categories.filter(
                        (cat) => cat.parent_id === null,
                      );

                      return (
                        <FormItem>
                          <FormLabel>Category</FormLabel>
                          <Popover open={open} onOpenChange={setOpen}>
                            <PopoverTrigger asChild>
                              <FormControl>
                                <Button
                                  variant="outline"
                                  role="combobox"
                                  aria-expanded={open}
                                  className="w-full justify-between"
                                >
                                  {field.value
                                    ? parentCategories.find(
                                        (category) =>
                                          category.id === field.value,
                                      )?.name
                                    : "Select category..."}
                                  <ChevronsUpDownIcon className="ml-2 h-4 w-4 shrink-0 opacity-50" />
                                </Button>
                              </FormControl>
                            </PopoverTrigger>
                            <PopoverContent className="w-full p-0">
                              <Command>
                                <CommandInput placeholder="Search category..." />
                                <CommandList>
                                  <CommandEmpty>
                                    No category found.
                                  </CommandEmpty>
                                  <CommandGroup>
                                    {parentCategories.map((category) => (
                                      <CommandItem
                                        key={category.id}
                                        value={category.name}
                                        onSelect={() => {
                                          field.onChange(category.id);
                                          // Clear subcategory when category changes
                                          form.setValue("subcategory", "");
                                          setOpen(false);
                                        }}
                                      >
                                        <CheckIcon
                                          className={cn(
                                            "mr-2 h-4 w-4",
                                            field.value === category.id
                                              ? "opacity-100"
                                              : "opacity-0",
                                          )}
                                        />
                                        {category.name}
                                      </CommandItem>
                                    ))}
                                  </CommandGroup>
                                </CommandList>
                              </Command>
                            </PopoverContent>
                          </Popover>
                          <FormMessage />
                        </FormItem>
                      );
                    }}
                  />

                  <FormField
                    control={form.control}
                    name="subcategory"
                    render={({ field }) => {
                      const [open, setOpen] = React.useState(false);
                      const selectedCategoryId = form.watch("category");
                      const subcategories = categories.filter(
                        (cat) => cat.parent_id === selectedCategoryId,
                      );

                      return (
                        <FormItem>
                          <FormLabel>Subcategory</FormLabel>
                          <Popover open={open} onOpenChange={setOpen}>
                            <PopoverTrigger asChild>
                              <FormControl>
                                <Button
                                  variant="outline"
                                  role="combobox"
                                  aria-expanded={open}
                                  className="w-full justify-between"
                                  disabled={!selectedCategoryId}
                                >
                                  {field.value
                                    ? subcategories.find(
                                        (subcategory) =>
                                          subcategory.id === field.value,
                                      )?.name
                                    : selectedCategoryId
                                      ? "Select subcategory..."
                                      : "Select category first"}
                                  <ChevronsUpDownIcon className="ml-2 h-4 w-4 shrink-0 opacity-50" />
                                </Button>
                              </FormControl>
                            </PopoverTrigger>
                            <PopoverContent className="w-full p-0">
                              <Command>
                                <CommandInput placeholder="Search subcategory..." />
                                <CommandList>
                                  <CommandEmpty>
                                    No subcategory found.
                                  </CommandEmpty>
                                  <CommandGroup>
                                    {subcategories.map((subcategory) => (
                                      <CommandItem
                                        key={subcategory.id}
                                        value={subcategory.name}
                                        onSelect={() => {
                                          field.onChange(subcategory.id);
                                          setOpen(false);
                                        }}
                                      >
                                        <CheckIcon
                                          className={cn(
                                            "mr-2 h-4 w-4",
                                            field.value === subcategory.id
                                              ? "opacity-100"
                                              : "opacity-0",
                                          )}
                                        />
                                        {subcategory.name}
                                      </CommandItem>
                                    ))}
                                  </CommandGroup>
                                </CommandList>
                              </Command>
                            </PopoverContent>
                          </Popover>
                          <FormMessage />
                        </FormItem>
                      );
                    }}
                  />
                </div>

                {/* Brand Field */}
                <FormField
                  control={form.control}
                  name="brand"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Brand</FormLabel>
                      <Select
                        onValueChange={field.onChange}
                        defaultValue={field.value}
                        disabled={availableBrands.length === 0}
                      >
                        <FormControl>
                          <SelectTrigger>
                            <SelectValue
                              placeholder={
                                availableBrands.length === 0
                                  ? "Select subcategory first"
                                  : "Select brand..."
                              }
                            />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          <SelectItem value="none">No brand</SelectItem>
                          {availableBrands.map((brand) => (
                            <SelectItem key={brand} value={brand}>
                              {brand}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="origin_location"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Origin Location</FormLabel>
                      <Select
                        onValueChange={field.onChange}
                        defaultValue={field.value}
                      >
                        <FormControl>
                          <SelectTrigger>
                            <SelectValue placeholder="Select location" />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          <SelectItem value="UK">UK</SelectItem>
                          <SelectItem value="Ghana">Ghana</SelectItem>
                        </SelectContent>
                      </Select>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="tags"
                  render={({ field }) => {
                    const [inputValue, setInputValue] = React.useState(
                      field.value?.join(", ") || "",
                    );

                    // Sync inputValue with field value when form is reset
                    React.useEffect(() => {
                      setInputValue(field.value?.join(", ") || "");
                    }, [field.value]);

                    const processTagsFromInput = (value: string) => {
                      const tags = value
                        .split(",")
                        .map((tag) => tag.trim())
                        .filter((tag) => tag.length > 0);
                      field.onChange(tags.length > 0 ? tags : []);
                    };

                    return (
                      <FormItem>
                        <FormLabel>Tags</FormLabel>
                        <FormControl>
                          <Textarea
                            placeholder="Enter tags separated by commas (e.g., electronics, smartphone, apple)"
                            className="min-h-[80px]"
                            value={inputValue}
                            onChange={(e) => {
                              setInputValue(e.target.value);
                            }}
                            onBlur={() => {
                              processTagsFromInput(inputValue);
                            }}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    );
                  }}
                />

                <FormField
                  control={form.control}
                  name="admin_notes"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Admin Notes</FormLabel>
                      <FormControl>
                        <Textarea
                          placeholder="Internal notes for administrators..."
                          className="min-h-[100px]"
                          {...field}
                        />
                      </FormControl>
                      <FormDescription>
                        Internal notes visible only to administrators
                      </FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </CardContent>
            </Card>

            <div className="space-y-6">
              {/* Return Policy */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <DollarSign className="h-5 w-5" />
                    Return Policy
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <FormField
                    control={form.control}
                    name="refundable"
                    render={({ field }) => (
                      <FormItem className="space-y-3">
                        <FormLabel>Is this product refundable?</FormLabel>
                        <FormControl>
                          <RadioGroup
                            onValueChange={(value) =>
                              field.onChange(value === "true")
                            }
                            value={field.value ? "true" : "false"}
                            className="flex flex-col space-y-1"
                          >
                            <FormItem className="flex items-center space-x-3 space-y-0">
                              <FormControl>
                                <RadioGroupItem value="true" />
                              </FormControl>
                              <FormLabel className="font-normal">Yes</FormLabel>
                            </FormItem>
                            <FormItem className="flex items-center space-x-3 space-y-0">
                              <FormControl>
                                <RadioGroupItem value="false" />
                              </FormControl>
                              <FormLabel className="font-normal">No</FormLabel>
                            </FormItem>
                          </RadioGroup>
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="refund_policy"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Refund Policy</FormLabel>
                        <FormControl>
                          <Input
                            placeholder="Refund policy details"
                            {...field}
                          />
                        </FormControl>
                        <FormDescription>
                          This field is automatically populated based on your
                          selection.
                        </FormDescription>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </CardContent>
              </Card>

              {/* Product Images */}
              <Card>
                <CardHeader>
                  <CardTitle>Product Images</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-6">
                    {/* Primary Image */}
                    <div>
                      <FormLabel className="text-sm font-medium">
                        Thumbnail Image *
                      </FormLabel>
                      <Input
                        ref={primaryImageInputRef}
                        type="file"
                        accept="image/*"
                        onChange={handlePrimaryImageChange}
                        className="mb-2 mt-1"
                      />
                      <FormDescription>
                        This is the image that is displayed on the shop page /
                        results page (required)
                      </FormDescription>

                      {primaryImagePreview && (
                        <div className="mt-4 relative inline-block">
                          <img
                            src={primaryImagePreview}
                            alt="Primary image preview"
                            className="w-32 h-32 object-cover rounded-md border-2 border-blue-500"
                          />
                          <div className="absolute top-1 left-1 bg-blue-500 text-white text-xs px-2 py-1 rounded">
                            Primary
                          </div>
                          <button
                            type="button"
                            onClick={removePrimaryImage}
                            className="absolute top-1 right-1 bg-red-500 text-white p-1 rounded-full hover:bg-red-600 text-xs"
                          >
                            ×
                          </button>
                        </div>
                      )}
                    </div>

                    {/* Shared Images */}
                    <div>
                      <FormLabel className="text-sm font-medium">
                        Shared Images (Optional)
                      </FormLabel>
                      <Input
                        ref={sharedImagesInputRef}
                        type="file"
                        accept="image/*"
                        multiple
                        onChange={handleSharedImagesChange}
                        className="mb-2 mt-1"
                      />
                      <FormDescription>
                        Upload additional product images that are displayed
                        across all variants.
                      </FormDescription>

                      {sharedImagePreviews.length > 0 && (
                        <div className="grid grid-cols-3 gap-4 mt-4">
                          {sharedImagePreviews.map((url, imageIndex) => (
                            <div key={imageIndex} className="relative">
                              <img
                                src={url}
                                alt={`Shared image ${imageIndex + 1}`}
                                className="w-full h-32 object-cover rounded-md"
                              />
                              <button
                                type="button"
                                onClick={() => removeSharedImage(imageIndex)}
                                className="absolute top-2 right-2 bg-red-500 text-white p-1 rounded-full hover:bg-red-600"
                              >
                                ×
                              </button>
                            </div>
                          ))}
                        </div>
                      )}
                    </div>
                  </div>
                </CardContent>
              </Card>

              {/* Type-specific Details */}
              <Card>
                <CardHeader>
                  <CardTitle>Product Details</CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  {productType === "external" ? (
                    <>
                      <FormField
                        control={form.control}
                        name="details.origin_name"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Origin Store Name</FormLabel>
                            <FormControl>
                              <Input placeholder="Store name" {...field} />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />

                      <FormField
                        control={form.control}
                        name="details.origin_url"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Product URL</FormLabel>
                            <FormControl>
                              <Input placeholder="https://..." {...field} />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />

                      <FormField
                        control={form.control}
                        name="details.is_affiliate"
                        render={({ field }) => (
                          <FormItem className="flex flex-row items-start space-x-3 space-y-0 rounded-md border p-4">
                            <FormControl>
                              <Checkbox
                                checked={field.value}
                                onCheckedChange={field.onChange}
                              />
                            </FormControl>
                            <div className="space-y-1 leading-none">
                              <FormLabel>Is Affiliate Product</FormLabel>
                              <FormDescription>
                                Check this if the product is associated with an
                                affiliate program
                              </FormDescription>
                            </div>
                          </FormItem>
                        )}
                      />

                      <FormField
                        control={form.control}
                        name="details.discount_code"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Discount Code (Optional)</FormLabel>
                            <FormControl>
                              <Input placeholder="Discount code" {...field} />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                    </>
                  ) : (
                    <>
                      <FormField
                        control={form.control}
                        name="details.weight_kg"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Weight (kg) *</FormLabel>
                            <FormControl>
                              <Input
                                type="number"
                                step="0.01"
                                min="0"
                                placeholder="0.00"
                                {...field}
                                value={field.value || ""}
                                onChange={(e) =>
                                  field.onChange(
                                    e.target.value
                                      ? Number(e.target.value)
                                      : undefined,
                                  )
                                }
                              />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />

                      <div>
                        <label className="text-sm font-medium mb-1 block">
                          Dimensions (cm) *
                        </label>
                        <div className="grid grid-cols-3 gap-4">
                          <FormField
                            control={form.control}
                            name="details.dimensions_cm.length"
                            render={({ field }) => (
                              <FormItem>
                                <FormControl>
                                  <Input
                                    type="number"
                                    placeholder="Length"
                                    {...field}
                                    value={field.value || ""}
                                    onChange={(e) =>
                                      field.onChange(
                                        e.target.value
                                          ? Number(e.target.value)
                                          : undefined,
                                      )
                                    }
                                  />
                                </FormControl>
                                <FormMessage />
                              </FormItem>
                            )}
                          />
                          <FormField
                            control={form.control}
                            name="details.dimensions_cm.width"
                            render={({ field }) => (
                              <FormItem>
                                <FormControl>
                                  <Input
                                    type="number"
                                    placeholder="Width"
                                    {...field}
                                    value={field.value || ""}
                                    onChange={(e) =>
                                      field.onChange(
                                        e.target.value
                                          ? Number(e.target.value)
                                          : undefined,
                                      )
                                    }
                                  />
                                </FormControl>
                                <FormMessage />
                              </FormItem>
                            )}
                          />
                          <FormField
                            control={form.control}
                            name="details.dimensions_cm.height"
                            render={({ field }) => (
                              <FormItem>
                                <FormControl>
                                  <Input
                                    type="number"
                                    placeholder="Height"
                                    {...field}
                                    value={field.value || ""}
                                    onChange={(e) =>
                                      field.onChange(
                                        e.target.value
                                          ? Number(e.target.value)
                                          : undefined,
                                      )
                                    }
                                  />
                                </FormControl>
                                <FormMessage />
                              </FormItem>
                            )}
                          />
                        </div>
                      </div>
                    </>
                  )}
                </CardContent>
              </Card>
            </div>
          </div>

          {/* Shipping & Duty */}
          <div className="mt-6">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Truck className="h-5 w-5" />
                  Shipping & Duty
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid gap-4">
                  {["Ghana", "Nigeria"].map((country, index) => {
                    // For display, prefer the value from shipping_rates if present,
                    // but keep the shipping_and_duty entry as the canonical form for the small duty form.
                    const compact =
                      (form.getValues("shipping_and_duty") as any) || [];
                    // Find first matching compact entry for the country to derive a display/initial value
                    const firstEntry = Array.isArray(compact)
                      ? (compact.find(
                          (r: any) => r.to_country === country,
                        ) as any)
                      : undefined;

                    return (
                      <div key={country} className="p-4 border rounded-lg">
                        <div className="mb-2">
                          <label className="text-sm font-medium">
                            To Country
                          </label>
                          <div className="mt-1 px-3 py-2 border rounded-md bg-muted text-sm">
                            {country}
                          </div>
                        </div>

                        <FormField
                          control={form.control}
                          name={`shipping_and_duty.${index}.duty`}
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel>Duty</FormLabel>
                              <FormControl>
                                <Input
                                  type="number"
                                  min="0"
                                  step="1"
                                  placeholder="Duty"
                                  value={field.value ?? ""}
                                  onChange={(e) =>
                                    field.onChange(
                                      e.target.value
                                        ? Number(e.target.value)
                                        : undefined,
                                    )
                                  }
                                />
                              </FormControl>
                              <FormMessage />
                            </FormItem>
                          )}
                        />
                      </div>
                    );
                  })}
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Product Variants */}
          {variantAttributes.length > 0 && (
            <div className="mt-6">
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Package2 className="h-5 w-5" />
                    Product Info
                  </CardTitle>
                  <p className="text-sm text-muted-foreground">
                    At least one variant is required. The first variant's
                    pricing will be used as the primary product data.
                  </p>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="grid gap-4">
                    {form
                      .watch("variants")
                      ?.variants?.map((_: any, variantIndex: number) => (
                        <div
                          key={variantIndex}
                          className="space-y-4 p-4 border rounded-lg"
                        >
                          <div className="flex justify-between items-center">
                            <h4 className="font-medium">
                              {variantIndex === 0
                                ? "Main Product Info"
                                : `Variant ${variantIndex}`}
                            </h4>
                            {variantIndex !== 0 && (
                              <Button
                                type="button"
                                variant="destructive"
                                size="sm"
                                onClick={() => removeVariant(variantIndex)}
                              >
                                Remove
                              </Button>
                            )}
                          </div>

                          {/* Variant Attributes */}
                          <div className="grid grid-cols-2 gap-4">
                            {variantAttributes.map((attr) => {
                              const hasCustom = (attr.values || []).includes(
                                "Custom",
                              );
                              return (
                                <FormField
                                  key={attr.name}
                                  control={form.control}
                                  name={
                                    `variants.variants.${variantIndex}.option_values.${attr.name}` as any
                                  }
                                  render={({ field }) => {
                                    const isCustomSelected =
                                      hasCustom && field.value === "Custom";

                                    return (
                                      <FormItem>
                                        <FormLabel>{attr.name}</FormLabel>
                                        <FormControl>
                                          <div className="space-y-2">
                                            {/* Select Dropdown */}
                                            {attr.options || attr.values ? (
                                              <Select
                                                onValueChange={(value) => {
                                                  // Always store as string, transform to object only on submit
                                                  field.onChange(value);
                                                }}
                                                value={
                                                  isCustomSelected
                                                    ? "Custom"
                                                    : typeof field.value ===
                                                        "string"
                                                      ? field.value
                                                      : typeof field.value ===
                                                            "object" &&
                                                          !field.value
                                                            ?.is_custom
                                                        ? field.value?.value
                                                        : ""
                                                }
                                              >
                                                <SelectTrigger>
                                                  <SelectValue
                                                    placeholder={`Select ${attr.name}`}
                                                  />
                                                </SelectTrigger>
                                                <SelectContent>
                                                  {(
                                                    attr.options || attr.values
                                                  ).map((option: any) => (
                                                    <SelectItem
                                                      key={option}
                                                      value={String(option)}
                                                    >
                                                      {String(option)}
                                                    </SelectItem>
                                                  ))}
                                                </SelectContent>
                                              </Select>
                                            ) : (
                                              <Input
                                                type="text"
                                                placeholder={`Enter ${attr.name}`}
                                                value={
                                                  typeof field.value ===
                                                  "string"
                                                    ? field.value
                                                    : ""
                                                }
                                                onChange={(e) => {
                                                  // Store as simple string, will transform on submit
                                                  field.onChange(
                                                    e.target.value,
                                                  );
                                                }}
                                              />
                                            )}

                                            {/* Custom Input Field - only show when custom is selected */}
                                            {isCustomSelected && (
                                              <div>
                                                <p className="text-sm text-gray-600 mb-1">
                                                  Custom {attr.name}:
                                                </p>
                                                <Input
                                                  type="text"
                                                  placeholder={`Enter custom ${attr.name}`}
                                                  value={getCustomAttributeValue(
                                                    variantIndex,
                                                    attr.name,
                                                  )}
                                                  onChange={(e) => {
                                                    // Store custom value in separate state
                                                    setCustomAttributeValue(
                                                      variantIndex,
                                                      attr.name,
                                                      e.target.value,
                                                    );
                                                  }}
                                                />
                                              </div>
                                            )}
                                          </div>
                                        </FormControl>
                                        <FormMessage />
                                      </FormItem>
                                    );
                                  }}
                                />
                              );
                            })}
                          </div>

                          {/* Variant Pricing */}
                          <div className="grid grid-cols-2 gap-4">
                            <FormField
                              control={form.control}
                              name={
                                `variants.variants.${variantIndex}.price` as any
                              }
                              render={({ field }) => (
                                <FormItem>
                                  <FormLabel>Base Price</FormLabel>
                                  <FormControl>
                                    <Input
                                      type="number"
                                      placeholder="0.00"
                                      {...field}
                                      value={String(field.value || "")}
                                      onChange={(e) =>
                                        field.onChange(
                                          e.target.value
                                            ? Number(e.target.value)
                                            : 0,
                                        )
                                      }
                                    />
                                  </FormControl>
                                  <FormMessage />
                                </FormItem>
                              )}
                            />

                            <FormField
                              control={form.control}
                              name={
                                `variants.variants.${variantIndex}.sale_price` as any
                              }
                              render={({ field }) => (
                                <FormItem>
                                  <FormLabel>Sale Price</FormLabel>
                                  <FormControl>
                                    <Input
                                      type="number"
                                      placeholder="0.00"
                                      {...field}
                                      value={String(field.value || "")}
                                      onChange={(e) =>
                                        field.onChange(
                                          e.target.value
                                            ? Number(e.target.value)
                                            : 0,
                                        )
                                      }
                                    />
                                  </FormControl>
                                  <FormMessage />
                                </FormItem>
                              )}
                            />
                          </div>

                          <div className="grid grid-cols-2 gap-4">
                            <FormField
                              control={form.control}
                              name={
                                `variants.variants.${variantIndex}.discount_percent` as any
                              }
                              render={({ field }) => (
                                <FormItem>
                                  <FormLabel>Discount Percentage</FormLabel>
                                  <FormControl>
                                    <Input
                                      type="number"
                                      placeholder="Auto-calculated"
                                      readOnly
                                      {...field}
                                      value={field.value || ""}
                                      className="bg-gray-50 cursor-not-allowed"
                                    />
                                  </FormControl>
                                  <FormDescription>
                                    Automatically calculated based on base price
                                    and sale price (shown as percentage)
                                  </FormDescription>
                                  <FormMessage />
                                </FormItem>
                              )}
                            />
                          </div>

                          {/* Variant Stock */}
                          <FormField
                            control={form.control}
                            name={
                              `variants.variants.${variantIndex}.stock` as any
                            }
                            render={({ field }) => (
                              <FormItem>
                                <FormLabel>Stock Quantity</FormLabel>
                                <FormControl>
                                  <Input
                                    type="number"
                                    min="0"
                                    placeholder="0"
                                    {...field}
                                    value={String(field.value || "")}
                                    onChange={(e) =>
                                      field.onChange(
                                        e.target.value
                                          ? Number(e.target.value)
                                          : 0,
                                      )
                                    }
                                  />
                                </FormControl>
                                <FormMessage />
                              </FormItem>
                            )}
                          />

                          {/* Variant Images */}
                          <div>
                            <FormLabel className="text-sm font-medium">
                              Variant Images
                            </FormLabel>
                            <Input
                              type="file"
                              accept="image/*"
                              multiple
                              onChange={(e) =>
                                handleVariantImagesChange(variantIndex, e)
                              }
                              className="mb-2 mt-1"
                            />
                            <FormDescription>
                              Upload images specific to this variant
                            </FormDescription>

                            {variantImagePreviews[variantIndex] &&
                              variantImagePreviews[variantIndex].length > 0 && (
                                <div className="grid grid-cols-3 gap-4 mt-4">
                                  {variantImagePreviews[variantIndex].map(
                                    (url, imageIndex) => (
                                      <div
                                        key={imageIndex}
                                        className="relative"
                                      >
                                        <img
                                          src={url}
                                          alt={`${variantIndex === 0 ? "Main Product" : `Variant ${variantIndex}`} image ${imageIndex + 1}`}
                                          className="w-full h-32 object-cover rounded-md"
                                        />
                                        <button
                                          type="button"
                                          onClick={() =>
                                            removeVariantImage(
                                              variantIndex,
                                              imageIndex,
                                            )
                                          }
                                          className="absolute top-2 right-2 bg-red-500 text-white p-1 rounded-full hover:bg-red-600"
                                        >
                                          ×
                                        </button>
                                      </div>
                                    ),
                                  )}
                                </div>
                              )}
                          </div>
                        </div>
                      ))}
                  </div>

                  <Button type="button" variant="outline" onClick={addVariant}>
                    Add Variant
                  </Button>
                </CardContent>
              </Card>
            </div>
          )}

          <div className="mt-6 flex justify-end gap-4">
            <Button
              variant="outline"
              type="button"
              onClick={() => {
                // Reset form and clear all file-related states
                form.reset(getDefaultValues());
                resetFileStates();
              }}
            >
              Reset Form
            </Button>
            <Button type="submit" disabled={isLoading}>
              {isLoading ? "Creating..." : "Create Product"}
            </Button>
          </div>
        </div>
      </form>
    </Form>
  );
}
